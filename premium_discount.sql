-- 升贴水计算规则优化版数据库结构

-- 创建条件字段管理表
CREATE TABLE `sd_premium_discount_condition_field`
(
    `id`          INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `field_code`  VARCHAR(50)  NOT NULL COMMENT '字段代码(如weight, region)',
    `field_name`  VARCHAR(100) NOT NULL COMMENT '字段名称(如重量, 地区)',
    `field_type`  ENUM('number', 'string', 'select') NOT NULL DEFAULT 'string' COMMENT '字段类型(number=数值型, string=字符串型, select=选择型)',
    `field_unit`  VARCHAR(20)  NULL COMMENT '字段单位(如kg, %, 等)',
    `select_options` TEXT NULL COMMENT '选择项配置(JSON格式，当field_type=select时使用)',
    `is_range`    TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '是否支持范围(1=支持范围, 0=单值)',
    `is_enabled`  TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否启用(1=启用, 0=禁用)',
    `sort_order`  INT          NOT NULL DEFAULT 100 COMMENT '排序权重',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_field_code` (`field_code`) COMMENT '字段代码唯一索引',
    KEY `idx_enabled_sort` (`is_enabled`, `sort_order`) COMMENT '启用状态和排序索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水条件字段管理表';

-- 插入默认条件字段
INSERT INTO `sd_premium_discount_condition_field` (`field_code`, `field_name`, `field_type`, `field_unit`, `is_range`, `sort_order`) VALUES
                                                                                                                                         ('weight', '重量', 'number', 'kg', 1, 10),
                                                                                                                                         ('moisture', '水分', 'number', '%', 1, 20),
                                                                                                                                         ('impurity', '杂质', 'number', '%', 1, 30),
                                                                                                                                         ('grade', '等级', 'select', NULL, 0, 40),
                                                                                                                                         ('region', '地区', 'select', NULL, 0, 50),
                                                                                                                                         ('color', '颜色级', 'select', NULL, 0, 60),
                                                                                                                                         ('length', '长度', 'number', 'mm', 1, 70),
                                                                                                                                         ('strength', '强力', 'number', 'cN/tex', 1, 80);

-- 更新等级字段的选择项
UPDATE `sd_premium_discount_condition_field` SET `select_options` = '["A级","B级","C级","D级"]' WHERE `field_code` = 'grade';

-- 更新地区字段的选择项
UPDATE `sd_premium_discount_condition_field` SET `select_options` = '["新疆","内地","进口"]' WHERE `field_code` = 'region';

-- 更新颜色级字段的选择项
UPDATE `sd_premium_discount_condition_field` SET `select_options` = '["白棉1级","白棉2级","白棉3级","淡点污棉1级","淡点污棉2级","淡点污棉3级"]' WHERE `field_code` = 'color';

-- 创建升贴水计算规则表
CREATE TABLE `sd_premium_discount_rule`
(
    `id`         INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_name`  VARCHAR(255) NOT NULL COMMENT '规则名称',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用(1=启用, 0=禁用)',
    `created_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_rule_name` (`rule_name`) COMMENT '规则名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水计算规则表';

-- 创建升贴水规则详情表（支持范围）
CREATE TABLE `sd_premium_discount_rule_detail`
(
    `id`              INT            NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id`         INT            NOT NULL COMMENT '关联规则ID',
    `field_id`        INT            NOT NULL COMMENT '关联条件字段ID',
    `condition_type`  ENUM('single', 'range') NOT NULL DEFAULT 'single' COMMENT '条件类型(single=单值, range=范围)',
    `operator`        ENUM('>', '<', '=', '>=', '<=', '!=', 'between', 'in') NOT NULL COMMENT '条件操作符',
    `condition_value` VARCHAR(255)   NULL COMMENT '条件值(单值时使用)',
    `min_value`       VARCHAR(100)   NULL COMMENT '最小值(范围时使用)',
    `max_value`       VARCHAR(100)   NULL COMMENT '最大值(范围时使用)',
    `discount_value`  DECIMAL(10, 2) NOT NULL COMMENT '升贴水值',
    `created_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_rule_id` (`rule_id`) COMMENT '规则ID索引',
    KEY               `idx_field_id` (`field_id`) COMMENT '字段ID索引',
    FOREIGN KEY (`rule_id`) REFERENCES `sd_premium_discount_rule`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`field_id`) REFERENCES `sd_premium_discount_condition_field`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水规则详情表';
