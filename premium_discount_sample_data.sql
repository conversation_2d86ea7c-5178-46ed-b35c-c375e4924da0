-- 升贴水规则示例数据

-- 首先添加与棉花系统对应的条件字段
INSERT INTO `sd_premium_discount_condition_field` (`field_code`, `field_name`, `field_type`, `field_unit`, `is_range`, `sort_order`) VALUES
('changdu', '长度', 'number', 'mm', 1, 10),
('qiangli', '强力', 'number', 'cN/tex', 1, 20),
('mazhi', '马值', 'number', '', 1, 30),
('huichaolv', '回潮率', 'number', '%', 1, 40),
('hanzalv', '含杂率', 'number', '%', 1, 50),
('zhengqidu', '整齐度', 'number', '%', 1, 60),
('leixing', '类型', 'select', '', 0, 70);

-- 更新类型字段的选择项
UPDATE `sd_premium_discount_condition_field` SET `select_options` = '["手摘棉","机采棉"]' WHERE `field_code` = 'leixing';

-- 添加升贴水规则
INSERT INTO `sd_premium_discount_rule` (`rule_name`, `is_enabled`) VALUES
('长度升贴水规则', 1),
('强力升贴水规则', 1),
('马值升贴水规则', 1),
('回潮率升贴水规则', 1),
('含杂率升贴水规则', 1),
('整齐度升贴水规则', 1),
('类型升贴水规则', 1);

-- 长度升贴水规则详情
-- 长度 >= 29mm 升水 +50
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `min_value`, `max_value`, `discount_value`) 
SELECT r.id, f.id, 'range', 'between', '29', '50', 50.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

-- 长度 27-28.9mm 升水 +20
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `min_value`, `max_value`, `discount_value`) 
SELECT r.id, f.id, 'range', 'between', '27', '28.9', 20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

-- 长度 < 26mm 贴水 -30
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '26', -30.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

-- 强力升贴水规则详情
-- 强力 >= 30 cN/tex 升水 +40
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '30', 40.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '强力升贴水规则' AND f.field_code = 'qiangli';

-- 强力 < 25 cN/tex 贴水 -25
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '25', -25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '强力升贴水规则' AND f.field_code = 'qiangli';

-- 马值升贴水规则详情
-- 马值 >= 4.5 升水 +30
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '4.5', 30.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '马值升贴水规则' AND f.field_code = 'mazhi';

-- 马值 < 3.5 贴水 -20
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '3.5', -20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '马值升贴水规则' AND f.field_code = 'mazhi';

-- 回潮率升贴水规则详情
-- 回潮率 > 8.5% 贴水 -15
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>', '8.5', -15.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '回潮率升贴水规则' AND f.field_code = 'huichaolv';

-- 含杂率升贴水规则详情
-- 含杂率 > 3% 贴水 -25
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>', '3', -25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '含杂率升贴水规则' AND f.field_code = 'hanzalv';

-- 含杂率 <= 1% 升水 +20
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<=', '1', 20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '含杂率升贴水规则' AND f.field_code = 'hanzalv';

-- 整齐度升贴水规则详情
-- 整齐度 >= 85% 升水 +25
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '85', 25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '整齐度升贴水规则' AND f.field_code = 'zhengqidu';

-- 整齐度 < 80% 贴水 -15
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '80', -15.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '整齐度升贴水规则' AND f.field_code = 'zhengqidu';

-- 类型升贴水规则详情
-- 手摘棉 升水 +35
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '=', '手摘棉', 35.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '类型升贴水规则' AND f.field_code = 'leixing';

-- 机采棉 贴水 -10
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '=', '机采棉', -10.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '类型升贴水规则' AND f.field_code = 'leixing';
