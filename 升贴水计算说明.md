# 升贴水计算系统使用说明

## 概述

升贴水计算系统是基于棉花质量指标自动计算价格调整的工具。系统根据预设的规则，对棉花的各项质量指标进行评估，自动计算出相应的升贴水值。

## 核心概念

### 升贴水
- **升水（正值）**：表示价格上调，质量优于标准时适用
- **贴水（负值）**：表示价格下调，质量低于标准时适用
- **最终基差** = 原始基差 + 升贴水

### 计算规则
系统支持多种条件类型的规则：
- **单值条件**：如 `长度 >= 29mm`
- **范围条件**：如 `长度在 27-28.9mm 之间`
- **选择条件**：如 `类型 = 手摘棉`

## 计算示例

根据运行结果，以下是三个典型的计算示例：

### 示例1：优质手摘棉
**基本指标：**
- 长度：29.5mm
- 强力：31.2 cN/tex
- 马值：4.8
- 回潮率：7.2%
- 含杂率：0.8%
- 整齐度：86.5%
- 类型：手摘棉
- 原始基差：100

**升贴水计算：**
- 长度升贴水 (>=29mm): +50
- 强力升贴水 (>=30 cN/tex): +40
- 马值升贴水 (>=4.5): +30
- 含杂率升贴水 (<=1%): +20
- 整齐度升贴水 (>=85%): +25
- 类型升贴水 (手摘棉): +35

**结果：**
- 总升贴水：+200
- 调整后基差：100 + 200 = 300

### 示例2：普通机采棉
**基本指标：**
- 长度：26.8mm
- 强力：24.5 cN/tex
- 马值：3.2
- 回潮率：9.1%
- 含杂率：3.5%
- 整齐度：78.2%
- 类型：机采棉
- 原始基差：50

**升贴水计算：**
- 强力升贴水 (<25 cN/tex): -25
- 马值升贴水 (<3.5): -20
- 回潮率升贴水 (>8.5%): -15
- 含杂率升贴水 (>3%): -25
- 整齐度升贴水 (<80%): -15
- 类型升贴水 (机采棉): -10

**结果：**
- 总升贴水：-110
- 调整后基差：50 + (-110) = -60

### 示例3：中等品质棉花
**基本指标：**
- 长度：27.5mm
- 强力：28.0 cN/tex
- 马值：4.0
- 回潮率：8.0%
- 含杂率：2.0%
- 整齐度：82.0%
- 类型：手摘棉
- 原始基差：75

**升贴水计算：**
- 长度升贴水 (27-28.9mm): +20
- 类型升贴水 (手摘棉): +35

**结果：**
- 总升贴水：+55
- 调整后基差：75 + 55 = 130

## 升贴水规则详情

### 长度规则
- 长度 >= 29mm：升水 +50
- 长度 27-28.9mm：升水 +20
- 长度 < 26mm：贴水 -30

### 强力规则
- 强力 >= 30 cN/tex：升水 +40
- 强力 < 25 cN/tex：贴水 -25

### 马值规则
- 马值 >= 4.5：升水 +30
- 马值 < 3.5：贴水 -20

### 回潮率规则
- 回潮率 > 8.5%：贴水 -15

### 含杂率规则
- 含杂率 > 3%：贴水 -25
- 含杂率 <= 1%：升水 +20

### 整齐度规则
- 整齐度 >= 85%：升水 +25
- 整齐度 < 80%：贴水 -15

### 类型规则
- 手摘棉：升水 +35
- 机采棉：贴水 -10

## 系统特点

1. **多规则并行**：多个规则可以同时生效，升贴水值累加计算
2. **灵活配置**：规则可以通过数据库动态配置和调整
3. **精确计算**：支持小数点精度计算
4. **实时应用**：可以实时应用于棉花交易定价

## 使用方法

1. **数据库初始化**：
   - 执行 `premium_discount.sql` 创建表结构
   - 执行 `premium_discount_sample_data.sql` 插入示例规则

2. **API调用**：
   - 使用 `calculatePremiumDiscount` 方法计算单个记录的升贴水
   - 使用 `calculatePremiumDiscountByData` 方法计算表单数据的升贴水

3. **页面访问**：
   - 访问升贴水计算器页面进行交互式计算

## 注意事项

- 升贴水规则需要根据实际市场情况定期调整
- 计算结果仅供参考，实际交易价格可能受其他因素影响
- 建议在正式使用前充分测试各种数据组合的计算结果
