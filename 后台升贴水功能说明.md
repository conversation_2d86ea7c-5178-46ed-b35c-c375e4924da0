# 后台升贴水功能使用说明

## 功能概述

已成功为后台管理系统添加了完整的升贴水功能，包括：

1. **升贴水展示**：在列表中显示升贴水和调整后基差
2. **导入时计算**：Excel导入时自动计算升贴水
3. **批量更新**：批量更新选中记录的升贴水
4. **单条更新**：单条记录的升贴水更新按钮

## 新增功能详情

### 1. 列表页面升贴水显示

**新增列**：
- **升贴水**：显示计算出的升贴水值，绿色表示升水，红色表示贴水
- **调整后基差**：原始基差 + 升贴水的结果，颜色区分变化方向

**显示效果**：
```
基差    升贴水      调整后基差
100     +50 (绿色)   150 (绿色加粗)
50      -30 (红色)   20 (红色加粗)
75      0 (灰色)     75 (正常加粗)
```

### 2. 导入时自动计算升贴水

**功能位置**：Excel导入处理逻辑中

**处理流程**：
1. 解析Excel数据
2. 自动计算每条记录的升贴水
3. 保存升贴水值和调整后基差到数据库
4. 完成导入

**代码位置**：`Apps/Back/Controller/ChaoshiController.class.php` 第269-275行

### 3. 批量更新升贴水

**按钮位置**：列表页面顶部操作区域

**使用方法**：
1. 勾选需要更新的记录
2. 点击"批量更新升贴水"按钮
3. 确认操作
4. 系统自动计算并更新所有选中记录

**功能特点**：
- 支持多选批量处理
- 显示处理进度和结果统计
- 自动刷新页面显示最新数据

### 4. 单条记录更新

**按钮位置**：升贴水列中的刷新图标

**使用方法**：
1. 点击升贴水列中的刷新图标
2. 确认更新操作
3. 系统重新计算该记录的升贴水

**适用场景**：
- 质量指标数据修改后
- 升贴水规则调整后
- 单条记录数据校验

## 升贴水计算规则

### 支持的质量指标

1. **长度（changdu）**：单位mm
2. **强力（qiangli）**：单位cN/tex
3. **马值（mazhi）**：无单位
4. **回潮率（huichaolv）**：单位%
5. **含杂率（hanzalv）**：单位%
6. **整齐度（zhengqidu）**：单位%
7. **类型（leixing）**：手摘棉/机采棉

### 默认规则示例

**长度规则**：
- >= 29mm: +50
- 27-28.9mm: +20
- < 26mm: -30

**强力规则**：
- >= 30 cN/tex: +40
- < 25 cN/tex: -25

**类型规则**：
- 手摘棉: +35
- 机采棉: -10

**其他规则**：
- 马值 >= 4.5: +30
- 马值 < 3.5: -20
- 回潮率 > 8.5%: -15
- 含杂率 > 3%: -25
- 含杂率 <= 1%: +20
- 整齐度 >= 85%: +25
- 整齐度 < 80%: -15

## 数据库配置

### 1. 添加字段

执行 `add_premium_discount_fields.sql` 为超市表添加升贴水字段：

```sql
-- 升贴水值字段
ALTER TABLE `sd_chaoshi` ADD COLUMN `premium_discount` DECIMAL(10,2) DEFAULT 0;

-- 调整后基差字段
ALTER TABLE `sd_chaoshi` ADD COLUMN `calculated_jicha` DECIMAL(10,2) DEFAULT 0;
```

### 2. 创建规则表

执行 `premium_discount_rules_sample.sql` 创建升贴水规则管理表：

- `sd_premium_discount_rule` - 规则主表
- `sd_premium_discount_condition_field` - 条件字段表
- `sd_premium_discount_rule_detail` - 规则详情表

## 技术实现

### 1. 后端方法

**核心计算方法**：
- `_calculatePremiumDiscountByRules()` - 计算升贴水
- `_checkCondition()` - 检查规则条件

**API接口**：
- `updatePremiumDiscountBatch()` - 批量更新
- `updatePremiumDiscountSingle()` - 单条更新

### 2. 前端交互

**JavaScript功能**：
- 批量选择和操作
- AJAX异步更新
- 进度提示和结果反馈
- 自动页面刷新

**UI优化**：
- 颜色区分升贴水正负值
- 图标按钮直观操作
- 加载动画和消息提示

## 使用流程

### 1. 初始化配置

```bash
# 1. 添加数据库字段
mysql -u用户名 -p数据库名 < add_premium_discount_fields.sql

# 2. 创建升贴水规则
mysql -u用户名 -p数据库名 < premium_discount_rules_sample.sql
```

### 2. 日常使用

1. **导入数据**：Excel导入时自动计算升贴水
2. **查看结果**：列表页面查看升贴水和调整后基差
3. **批量更新**：选择记录后批量更新升贴水
4. **单条更新**：点击刷新图标更新单条记录

### 3. 规则管理

- 通过数据库直接修改升贴水规则
- 支持启用/禁用规则
- 可以添加新的质量指标和规则

## 注意事项

1. **数据完整性**：确保质量指标数据的准确性
2. **规则更新**：修改规则后需要重新计算历史数据
3. **性能考虑**：大批量更新时可能需要较长时间
4. **权限控制**：升贴水更新功能需要适当的权限管理

## 扩展建议

1. **规则管理界面**：开发可视化的规则管理页面
2. **历史记录**：保存升贴水计算的历史记录
3. **批量导出**：支持升贴水数据的批量导出
4. **定时任务**：设置定时任务自动更新升贴水

## 故障排除

### 常见问题

1. **升贴水显示为0**：
   - 检查升贴水规则是否正确配置
   - 确认质量指标数据是否完整

2. **批量更新失败**：
   - 检查数据库连接
   - 确认升贴水规则表是否存在

3. **导入时不计算升贴水**：
   - 检查导入逻辑是否包含升贴水计算
   - 确认升贴水规则是否启用

### 调试方法

1. 查看升贴水规则：
```sql
SELECT * FROM sd_premium_discount_rule WHERE is_enabled = 1;
```

2. 检查字段映射：
```sql
SELECT * FROM sd_premium_discount_condition_field;
```

3. 验证计算结果：
```sql
SELECT id, jicha, premium_discount, calculated_jicha 
FROM sd_chaoshi 
WHERE premium_discount IS NOT NULL 
LIMIT 10;
```
