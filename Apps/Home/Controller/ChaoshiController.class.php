<?php
namespace Home\Controller;
use Think\Controller;

class ChaoshiController extends CommController {

	function _initialize(){

		if(empty(session('uid'))){
			$this->redirect('Passport/login');
			die;
		}
		parent::_initialize();
// print_R($this->session_user);
		//toexcel，lists共用
		//大导航，新疆棉 地产棉 国储棉 进口棉
		$this->chandi = $chandi = M('Cat')->where(['pid'=>0])->order('id asc')->select();
		//def表示大导航
		$this->def = $def = I('get.def') == '' ? $chandi[0]['id'] : I('get.def');
		$this->chandi_default = M('Cat')->where(['pid'=>$def])->order('id asc')->select();
		// print_r($this->chandi_default);
		$this->jiaohuodi = M('<PERSON><PERSON>huodi')->where(['pid'=>0])->order('id asc')->select();


		//cd为父产地，查询其子产地
		if(I('get.cd') != '' && I('get.cd') !='|'){
			$cd = substr(I('get.cd') , 1, -1);
			$cd_arr = explode('|', $cd);
			$cd2 = [];
			foreach($cd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Cat')->where(['id'=>substr($v, 0 , -1)])->find();
				$cd2[$c['id']] =M('Cat')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($cd2);
			$this->cd2 = $cd2;
		}

		//孙产地
		$xx = $cd2;
		foreach($xx as $k=>$v){
			foreach($v as $k2=>$v2){
				$xx[$k][$k2]['children'] = M('Cat')->where(['pid'=>$v2['id']])->order('order_id desc, id asc')->select();
			}			
		}
		// print_r($xx);
		$this->xx = $xx;


		//jhd为父交货地，查询其子产地
		if(I('get.jhd') != '' && I('get.jhd') !='|'){
			$jhd = substr(I('get.jhd') , 1, -1);
			$jhd_arr = explode('|', $jhd);
			$jhd2 = [];
			foreach($jhd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Jiaohuodi')->where(['id'=>substr($v, 0 , -1)])->find();
				$jhd2[$c['id']] =M('Jiaohuodi')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($jhd2);
			$this->jhd2 = $jhd2;
		}
	}

	function cancelFavor(){
		if(empty(session('uid'))){
			die(json_encode(['code'=>0, 'msg'=>'请登录']));
		}

		if(I('get.id')){
			M('Favor')->where(['uid'=>session('uid'), 'favor_id'=>I('get.id')])->delete();
			die(json_encode(['code'=>1, 'msg'=>'取消成功']));
		}
	}

	function addFavor(){
		if(empty(session('uid'))){
			die(json_encode(['code'=>0, 'msg'=>'请登录']));
		}

		if(I('get.id')){
			if(M('Favor')->where(['uid'=>session('uid'), 'favor_id'=>I('get.id')])->find()){
				die(json_encode(['code'=>0, 'msg'=>'已收藏过该条记录']));
			}else{
				M('Favor')->add(['uid'=>session('uid'), 'favor_id'=>I('get.id'), 'add_time'=>time()]);
				die(json_encode(['code'=>1, 'msg'=>'收藏成功']));
			}
		}
	}


function rDingzhi(){

		// print_r($_POST);
		// die;
		if(empty(session('uid'))){
			die(json_encode(['code'=>0,'msg'=>'请登录']));
		}

		$d = M('Dingzhi');
		$d->create();
		$d->url=$_POST['url'];
		$d->dingzhi_no = 'D'.date('YmdHis');
		
		$d->changdu = I('post.changdu_from').'-'.I('post.changdu_to');
		$d->qiangli = I('post.qiangli_from').'-'.I('post.qiangli_to');
		$d->mazhi = I('post.mazhi_from').'-'.I('post.mazhi_to');
		$d->huichaolv = I('post.huichaolv_from').'-'.I('post.huichaolv_to');
		$d->hanzalv = I('post.hanzalv_from').'-'.I('post.hanzalv_to');
		$d->zhengqidu = I('post.zhengqidu_from').'-'.I('post.zhengqidu_to');

		$d->bm123 = I('post.bm123_from').'-'.I('post.bm123_to');
		$d->bm45 = I('post.bm45_from').'-'.I('post.bm45_to');
		$d->ddw123 = I('post.ddw123_from').'-'.I('post.ddw123_to');
		$d->dhr123 = I('post.dhr123_from').'-'.I('post.dhr123_to');
		$d->hr12 = I('post.hr12_from').'-'.I('post.hr12_to');


		$d->uid=session('uid');
		$d->add_time = time();
		$d->add();
		// $this->redirect('dziframe');
		// echo('<script>window.parent.window.location.href = "'.U('dingzhi').'"</script>');
		die(json_encode(['code'=>1, 'msg'=>'操作成功']));
	}



	function test(){

		$indexKey = array('order_sn','name','business','type','total','address','tel','moble','time1','status');       
		//excel表头内容
		$header = array('order_sn'=>'订单编号','name'=>'收货人','business'=>'商户名称','type'=>'支付方式','total'=>'订单金额','address'=>'收货地址','tel'=>'手机','moble'=>'电话','time1'=>'下单时间','status'=>'订单状态');
		array_unshift($list,$header);//将查询到的订单数据和表头内容合并,构造成数组list
		//toExcel($list,'1',$indexKey,1,true);
		$this->toExcel($list,'1',$indexKey,1,true);
	}


	function index(){
		if(I('get.act')=='excel'){
			if(empty($this->session_user['id'])){
				// $this->redirect('Passport/login');
				die(json_encode(['code'=>0, 'msg'=>'请登录']));
			}
			$this->excel();
		}else{
			$this->lists();
		}
	}



	function excel(){
		$where = '`id` > 0 ';

//搜索地区
$cd = substr(I('get.cd'), 1, -1);
$cd2 = substr(I('get.cd2'), 1, -1);
$cd3 = substr(I('get.cd3'), 1, -1);

$cda = explode('|', $cd);
$cda2 = explode('|', $cd2);
$cda3 = explode('|', $cd3);
// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含于第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($cda as $k=>$v){
	foreach($cda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($cda[$k]);
			break;
		}
	}
}

foreach($cda2 as $k2=>$v2){
	foreach($cda3 as $k3=>$v3){
		if(strrpos($v3, $v2) !== false){
			// array_splice($cda2, $k2, 1);
			unset($cda2[$k2]);
			break;
		}
	}
}

// print_r('------------');
// print_r($cda);
// print_r($cda2);
// print_r($cda3);

$lasta = array_merge($cda, $cda2,$cda3);
// print_r($lasta);
		
		if($lasta){
			$sql = '';
			foreach($lasta as $k=>$v){
				if($v){
					$sql .= '`path`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}
		

//交货地开始
$jhd = substr(I('get.jhd'), 1, -1);
$jhd2 = substr(I('get.jhd2'), 1, -1);


$jhda = explode('|', $jhd);
$jhda2 = explode('|', $jhd2);

// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($jhda as $k=>$v){
	foreach($jhda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($jhda[$k]);
			break;
		}
	}
}


$lasta2 = array_merge($jhda, $jhda2);
// print_r($lasta2);
		
		if($lasta2){
			$sql = '';
			foreach($lasta2 as $k=>$v){
				if($v){
					$sql .= '`path_cangku`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.leixing') !='|'&& I('get.leixing') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.leixing'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`leixing`  like "%'.$v.'%"  or ';
				}
			}

			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			//`id` > 0 and (`leixing` like "%手摘棉%" or `leixing` like "%机采棉%" )
			// print_r($where);
		}

		if(I('get.pihao_kunkao') !='|' && I('get.pihao_kunkao') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.pihao_kunkao'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`year`  = "'.substr($v, 2, 2).'"  or ';
				}
			}
			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.sk')!=''){
			if(strpos(I('get.sk'), ',') !== false){
				$where .= ' and `pihao_kunkao` in ('.I('get.sk').')';
			}else{
				$where .=' and (`pihao_kunkao` like "%'.I('get.sk').'%" or `jiagongchang` like "%'.I('get.sk').'%" or `cangchumingcheng` like "%'.I('get.sk').'%" ) ';
			}
			
		}


		if(I('get.changdu_from')!=''){
			$changdu_from = I('get.changdu_from') == 25 ? 0 : I('get.changdu_from');
			$where .= ' and `changdu` >= '.$changdu_from;
		}

		if(I('get.changdu_to')!=''){
			$changdu_to = I('get.changdu_to') == 32 ? 50 : I('get.changdu_to');
			$where .= ' and `changdu` <= '.$changdu_to;
		}

		
		if(I('get.mazhi_from')!=''){
			$mazhi_from = I('get.mazhi_from') == 2.5 ? 0 : I('get.mazhi_from');
			$where .= ' and `mazhi` >= '.$mazhi_from;
		}

		if(I('get.mazhi_to')!=''){
			$mazhi_to = I('get.mazhi_to') == 5.5 ? 10 : I('get.mazhi_to');
			$where .= ' and `mazhi` <= '.$mazhi_to;
		}

		if(I('get.qiangli_from')!=''){
			$qiangli_from = I('get.qiangli_from') == 25 ? 0 : I('get.qiangli_from');
			$where .= ' and `qiangli` >= '.$qiangli_from;
		}

		if(I('get.qiangli_to')!=''){
			$qiangli_to = I('get.qiangli_to') == 32 ? 50 : I('get.qiangli_to');
			$where .= ' and `qiangli` <= '.$qiangli_to;
		}

		if(I('get.huichaolv_from')!=''){
			$huichaolv_from = I('get.huichaolv_from') == 0 ? 0 : I('get.huichaolv_from');
			$where .= ' and `huichaolv` >= '.$huichaolv_from;
		}

		if(I('get.huichaolv_to')!=''){
			$huichaolv_to = I('get.huichaolv_to') == 10 ? 25 : I('get.huichaolv_to');
			$where .= ' and `huichaolv` <= '.$huichaolv_to;
		}

		if(I('get.zhengqidu_from')!=''){
			$zhengqidu_from = I('get.zhengqidu_from') == 77 ? 0 : I('get.zhengqidu_from');
			$where .= ' and `zhengqidu` >= '.$zhengqidu_from;
		}

		if(I('get.zhengqidu_to')!=''){
			$zhengqidu_to = I('get.zhengqidu_to') == 90 ? 100 : I('get.zhengqidu_to');
			$where .= ' and `zhengqidu` <= '.$zhengqidu_to;
		}

		if(I('get.hanzalv_from')!=''){
			$hanzalv_from = I('get.hanzalv_from') == 0 ? 0 : I('get.hanzalv_from');
			$where .= ' and `hanzalv` >= '.$hanzalv_from;
		}

		if(I('get.hanzalv_to')!=''){
			$hanzalv_to = I('get.hanzalv_to') == 6 ? 100 : I('get.hanzalv_to');
			$where .= ' and `hanzalv` <= '.$hanzalv_to;
		}


		if(I('get.bm123_from')!=''){
			$where .= ' and `bm123` >= '.I('get.bm123_from');
		}

		if(I('get.bm123_to')!=''){
			$where .= ' and `bm123` <= '.I('get.bm123_to');
		}



		if(I('get.bm45_from')!=''){
			$where .= ' and `bm45` >= '.I('get.bm45_from');
		}

		if(I('get.bm45_to')!=''){
			$where .= ' and `bm45` <= '.I('get.bm45_to');
		}



		if(I('get.ddw123_from')!=''){
			$where .= ' and `ddw123` >= '.I('get.ddw123_from');
		}

		if(I('get.ddw123_to')!=''){
			$where .= ' and `ddw123` <= '.I('get.ddw123_to');
		}	

		if(I('get.dhr123_from')!=''){
			$where .= ' and `dhr123` >= '.I('get.dhr123_from');
		}

		if(I('get.dhr123_to')!=''){
			$where .= ' and `dhr123` <= '.I('get.dhr123_to');
		}


		if(I('get.hr12_from')!=''){
			$where .= ' and `hr12` >= '.I('get.hr12_from');
		}

		if(I('get.hr12_to')!=''){
			$where .= ' and `hr12` <= '.I('get.hr12_to');
		}


		if(I('get.jiaohuodi')!=''){
			$where .= ' and `jiaohuodi` = "'.I('get.jiaohuodi').'"';
		}


		if(I('get.hot') == 1){
			$where .= ' and `is_hot` = 1';
		}


		if(I('get.jiagongchang')!=''){
			$where .= ' and `jiagongchang` like "%'.I('get.jiagongchang').'%"';
		}


		if(I('get.cangchumingcheng')!=''){
			$where .= ' and `cangchumingcheng` like "%'.I('get.cangchumingcheng').'%"';
		}

		if(I('get.heyue')!=''){
			if(I('get.heyue') == 'jicha'){
				$where .= ' and `dianjiaheyue`> 0';
			}else{
				$where .= ' and `dianjiaheyue` like "%'.I('get.heyue').'%"';
			}
		}

		if(I('get.dan')!=''){
			$where .= ' and `dan_type` = "'.I('get.dan').'"';
		}


		

		if(I('get.zuiyou')){
			$order = ' `jicha` is null, `jicha` asc';
		}else if(I('get.xiangtong')){
			$order = '`'.I('get.xiangtong').'` asc';
		}else if(I('get.jicha')){
			$order = ' `jicha` is null, `jicha` asc ';
		}else{
			$order = ' `jicha` is null, `jicha` asc ';
		}

		// print_r($where);
		// die;
		
		$count =  M('Chaoshi')->where($where)->count();

		if(!empty(I('get.zuiyou')) && I('get.zuiyou') <= $count){
			$count = I('get.zuiyou');
		}

		$this->count = $count;

		//普通会员默认只导出50条，否则无限制

		$export_count = I('get.export_count');
		// $export_count = $this->session_user['member_type'] == 1 ? 45000 : 50;
		$page = new \Think\Page($count,$export_count);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order($order)->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			if($v['path']){
				$path = explode('-', substr($v['path'], 0 , -1));
				$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[2]])->getField('name');
			}

			$level2_id= M('Gongchang')->where(['title'=>$v['jiagongchang']])->getField('level2');
			$lists[$k]['chandi'] = M('Cat')->where(['id'=>$level2_id])->getField('name');

			$level2_id= M('Cangku')->where(['title'=>$v['cangchumingcheng']])->getField('level2');
			$lists[$k]['cangkusuozaidi'] = M('Jiaohuodi')->where(['id'=>$level2_id])->getField('name');

			$lists[$k]['daima'] = M('Excel_file')->where(['id'=>$v['file_id']])->getField('title');
			
		}

		// print_r(M('Chaoshi')->_sql());
		// // print_r(count($lists));
		// die;

		$this->lists = $lists;

		if($this->session_user['member_type'] == 1){
			//vip
				$indexKey = array(
					'daima',
					'pihao_kunkao',
					'leixing',
					'yanseji_pinji',
					'mazhi',
					'changdu',
					'qiangli',
					'hanzalv',
					'huichaolv',
					'gongzhong',
					'maozhaong',
					'zhengqidu',
					'ygzl',
					'jiagongchang',
					'cangchumingcheng',
					'jicha',
					'beizhu',
					'dianjiaheyue',
					'baoshu',
					'chandi',
					'cangkusuozaidi',
				);       
				//excel表头内容
				$header = array(
					'daima'=>'代码',
					'pihao_kunkao'=>'批号',
					'leixing'=>'类型',
					'yanseji_pinji'=>'品级',
					'mazhi'=>'马值',
					'changdu'=>'长度',
					'qiangli'=>'强力',
					'hanzalv'=>'含杂',
					'huichaolv'=>'回潮',
					'gongzhong'=>'公重(吨)',
					'maozhaong'=>'毛重(吨)',
					'zhengqidu'=>'整齐度',
					'ygzl'=>'扎工质量',
					'jiagongchang'=>'加工厂',
					'cangchumingcheng'=>'仓库',
					'jicha'=>'基差',
					'beizhu'=>'备注',
					'dianjiaheyue'=>'合约',
					'baoshu'=>'包数',
					'chandi'=>'产地',
					'cangkusuozaidi'=>'仓库所在地',
					
				);
		}else{

				$indexKey = array(
					'pihao_kunkao',
					'leixing',
					'yanseji_pinji',
					'mazhi',
					'changdu',
					'qiangli',
					'hanzalv',
					'huichaolv',
					'gongzhong',
					'maozhaong',
					'zhengqidu',
					'ygzl',
					'jiagongchang',
					'cangchumingcheng',
					'jicha',
					'dianjiaheyue',
					'baoshu',
					'chandi',
					'cangkusuozaidi',
				);       
				//excel表头内容
				$header = array(
					'pihao_kunkao'=>'批号',
					'leixing'=>'类型',
					'yanseji_pinji'=>'品级',
					'mazhi'=>'马值',
					'changdu'=>'长度',
					'qiangli'=>'强力',
					'hanzalv'=>'含杂',
					'huichaolv'=>'回潮',
					'gongzhong'=>'公重(吨)',
					'maozhaong'=>'毛重(吨)',
					'zhengqidu'=>'整齐度',
					'ygzl'=>'轧工质量',
					'jiagongchang'=>'加工厂',
					'cangchumingcheng'=>'仓库',
					'jicha'=>'基差',
					'dianjiaheyue'=>'合约',
					'baoshu'=>'包数',
					'chandi'=>'产地',
					'cangkusuozaidi'=>'仓库所在地',
					
				);
		}

		array_unshift($lists,$header);//将查询到的订单数据和表头内容合并,构造成数组list

		// print_r($lists);
		// die;
		$this->toExcel($lists,'export_'.date('Y-m-d'),$indexKey,1,true);
		
	}

	function lists(){
		$where = '`id` > 0 ';

//产地开始
$cd = substr(I('get.cd'), 1, -1);
$cd2 = substr(I('get.cd2'), 1, -1);
$cd3 = substr(I('get.cd3'), 1, -1);

$cda = explode('|', $cd);
$cda2 = explode('|', $cd2);
$cda3 = explode('|', $cd3);
// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($cda as $k=>$v){
	foreach($cda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($cda[$k]);
			break;
		}
	}
}

//看三级中是否包含二级
foreach($cda2 as $k2=>$v2){
	foreach($cda3 as $k3=>$v3){
		if(strrpos($v3, $v2) !== false){
			// array_splice($cda2, $k2, 1);
			unset($cda2[$k2]);
			break;
		}
	}
}

// print_r('------------');
// print_r($cda);
// print_r($cda2);
// print_r($cda3);

$lasta = array_merge($cda, $cda2,$cda3);
// print_r($lasta);
		
		if($lasta){
			$sql = '';
			foreach($lasta as $k=>$v){
				if($v){
					$sql .= '`path`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}
		

//交货地开始
$jhd = substr(I('get.jhd'), 1, -1);
$jhd2 = substr(I('get.jhd2'), 1, -1);


$jhda = explode('|', $jhd);
$jhda2 = explode('|', $jhd2);

// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($jhda as $k=>$v){
	foreach($jhda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($jhda[$k]);
			break;
		}
	}
}


$lasta2 = array_merge($jhda, $jhda2);
// print_r($lasta2);
		
		if($lasta2){
			$sql = '';
			foreach($lasta2 as $k=>$v){
				if($v){
					$sql .= '`path_cangku`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.leixing') !='|'&& I('get.leixing') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.leixing'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`leixing`  like "%'.$v.'%"  or ';
				}
			}

			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			//`id` > 0 and (`leixing` like "%手摘棉%" or `leixing` like "%机采棉%" )
			// print_r($where);
		}

		if(I('get.pihao_kunkao') !='|' && I('get.pihao_kunkao') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.pihao_kunkao'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`year`  = "'.substr($v, 2, 2).'"  or ';
				}
			}
			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			// print_r($where);
		}


		if(trim(I('get.sk'))!=''){
			if(strpos(I('get.sk'), ',') !== false){
				$where .= ' and `pihao_kunkao` in ('.trim(I('get.sk')).')';
			}else{
				$where .=' and (`pihao_kunkao` like "%'.trim(I('get.sk')).'%" or `jiagongchang` like "%'.trim(I('get.sk')).'%" or `cangchumingcheng` like "%'.trim(I('get.sk')).'%" ) ';
			}
			
		}

		// echo $where;
		// die;


		if(I('get.changdu_from')!=''){
			$changdu_from = I('get.changdu_from') == 25 ? 0 : I('get.changdu_from');
			$where .= ' and `changdu` >= '.$changdu_from;
		}

		if(I('get.changdu_to')!=''){
			$changdu_to = I('get.changdu_to') == 32 ? 50 : I('get.changdu_to');
			$where .= ' and `changdu` <= '.$changdu_to;
		}

		
		if(I('get.mazhi_from')!=''){
			$mazhi_from = I('get.mazhi_from') == 2.5 ? 0 : I('get.mazhi_from');
			$where .= ' and `mazhi` >= '.$mazhi_from;
		}

		if(I('get.mazhi_to')!=''){
			$mazhi_to = I('get.mazhi_to') == 5.5 ? 10 : I('get.mazhi_to');
			$where .= ' and `mazhi` <= '.$mazhi_to;
		}

		if(I('get.qiangli_from')!=''){
			$qiangli_from = I('get.qiangli_from') == 25 ? 0 : I('get.qiangli_from');
			$where .= ' and `qiangli` >= '.$qiangli_from;
		}

		if(I('get.qiangli_to')!=''){
			$qiangli_to = I('get.qiangli_to') == 32 ? 50 : I('get.qiangli_to');
			$where .= ' and `qiangli` <= '.$qiangli_to;
		}

		if(I('get.huichaolv_from')!=''){
			$huichaolv_from = I('get.huichaolv_from') == 0 ? 0 : I('get.huichaolv_from');
			$where .= ' and `huichaolv` >= '.$huichaolv_from;
		}

		if(I('get.huichaolv_to')!=''){
			$huichaolv_to = I('get.huichaolv_to') == 10 ? 25 : I('get.huichaolv_to');
			$where .= ' and `huichaolv` <= '.$huichaolv_to;
		}

		if(I('get.zhengqidu_from')!=''){
			$zhengqidu_from = I('get.zhengqidu_from') == 77 ? 0 : I('get.zhengqidu_from');
			$where .= ' and `zhengqidu` >= '.$zhengqidu_from;
		}

		if(I('get.zhengqidu_to')!=''){
			$zhengqidu_to = I('get.zhengqidu_to') == 90 ? 100 : I('get.zhengqidu_to');
			$where .= ' and `zhengqidu` <= '.$zhengqidu_to;
		}

		if(I('get.hanzalv_from')!=''){
			$hanzalv_from = I('get.hanzalv_from') == 0 ? 0 : I('get.hanzalv_from');
			$where .= ' and `hanzalv` >= '.$hanzalv_from;
		}

		if(I('get.hanzalv_to')!=''){
			$hanzalv_to = I('get.hanzalv_to') == 6 ? 100 : I('get.hanzalv_to');
			$where .= ' and `hanzalv` <= '.$hanzalv_to;
		}



			if(I('get.bm123_from')!=''){
				$where .= ' and `bm123` >= '.I('get.bm123_from');
			}

			if(I('get.bm123_to')!=''){
				$where .= ' and `bm123` <= '.I('get.bm123_to');
			}


			if(I('get.bm45_from')!=''){
				$where .= ' and `bm45` >= '.I('get.bm45_from');
			}

			if(I('get.bm45_to')!=''){
				$where .= ' and `bm45` <= '.I('get.bm45_to');
			}


			if(I('get.ddw123_from')!=''){
				$where .= ' and `ddw123` >= '.I('get.ddw123_from');
			}

			if(I('get.ddw123_to')!=''){
				$where .= ' and `ddw123` <= '.I('get.ddw123_to');
			}	



			if(I('get.dhr123_from')!=''){
				$where .= ' and `dhr123` >= '.I('get.dhr123_from');
			}

			if(I('get.dhr123_to')!=''){
				$where .= ' and `dhr123` <= '.I('get.dhr123_to');
			}




			if(I('get.hr12_from')!=''){
				$where .= ' and `hr12` >= '.I('get.hr12_from');
			}

			if(I('get.hr12_to')!=''){
				$where .= ' and `hr12` <= '.I('get.hr12_to');
			}



		if(I('get.jiaohuodi')!=''){
			$where .= ' and `jiaohuodi` = "'.I('get.jiaohuodi').'"';
		}


		if(I('get.hot') == 1){
			$where .= ' and `is_hot` = 1';
		}


		if(I('get.jiagongchang')!=''){
			$where .= ' and `jiagongchang` like "%'.I('get.jiagongchang').'%"';
		}


		if(I('get.cangchumingcheng')!=''){
			$where .= ' and `cangchumingcheng` like "%'.I('get.cangchumingcheng').'%"';
		}


		if(I('get.heyue')!=''){
			if(I('get.heyue') == 'jicha'){
				$where .= ' and `dianjiaheyue`> 0';
			}else{
				$where .= ' and `dianjiaheyue` like "%'.I('get.heyue').'%"';
			}
			
		}

		if(I('get.dan')!=''){
			$where .= ' and `dan_type` = "'.I('get.dan').'"';
		}


		

		if(I('get.zuiyou')){
			$order = '`jicha` asc';
		}else if(I('get.xiangtong')){
			$order = '`'.I('get.xiangtong').'` asc';
		}else if(I('get.jicha')){
			$order = ' `jicha` is null, `jicha` asc ';
		}else{
			$order = ' `jicha` is null, `jicha` asc ';
		}

		// print_r($where);
		// die;
		
		$count =  M('Chaoshi')->where($where)->count();

		if(!empty(I('get.zuiyou')) && I('get.zuiyou') <= $count){
			$count = I('get.zuiyou');
		}

		$this->count = $count;

		// echo $this->count;
		$page = new \Think\Page($count,20);
		$page->setConfig('theme', "%FIRST% %UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order($order)->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			if($v['path']){
				$path = explode('-', substr($v['path'], 0 , -1));
				$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[2]])->getField('name');
			}

			if($v['path_cangku']){
				$path = explode('-', substr($v['path_cangku'], 0 , -1));
				$lists[$k]['diqu_text_cangku'] = M('Jiaohuodi')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Jiaohuodi')->where(['id'=>$path[1]])->getField('name').' '
				.M('Jiaohuodi')->where(['id'=>$path[2]])->getField('name');
			}
			$lists[$k]['gongzhong'] = round($v['gongzhong'],3);
			$lists[$k]['huichaolv'] = round($v['huichaolv'],1);
			$lists[$k]['is_favor'] = M('Favor')->where(['uid'=>session('uid'), 'favor_id'=>$v['id']])->find() ? 1 : 0;
			$daima = M('Excel_file')->where(['id'=>$v['file_id']])->getField('title');
			$xx = explode('，', $daima);
			$lists[$k]['daima_cut'] = $xx[1];
			
		}
		$this->lists = $lists;
		// print_r(M('Chaoshi')->_sql());
		$this->display();
		
	}

	function view(){
		if(I('get.id')){
			$this->res = $res =  M('Chaoshi')->find(I('get.id'));

			// print_r($res);


			$r = file_get_contents('http://www.oureway.com/search.shtml?keyword='.$res['pihao_kunkao']);
			$p = '/<li class=\"manufacturer_2\">(.*?)<a href=\"(.*?)\"\s+target(.*?)>(.*?)<\/a><\/li>/ism';
			preg_match_all( $p, $r, $bb);
			 // print_r($bb[2][0]);

			$url = 'http://www.oureway.com/'.$bb[2][0];

			$ret = file_get_contents($url);
			$pat = '/<div class=\"quantity_list\" style=\"background:#fff;\">(.*?)\<\/table>/ism';
			 preg_match_all( $pat, $ret, $xx);
			 // print_r($xx);
			 $this->biao = $xx[0][0];
			 if(I('get.type')=='print'){
				$this->display('print');
			 }else{
				$this->display();
			 }

		}
	}

	/**
	 * 计算升贴水
	 */
	function calculatePremiumDiscount(){
		if(I('get.id')){
			$chaoshi = M('Chaoshi')->find(I('get.id'));
			if(!$chaoshi){
				die(json_encode(['code'=>0, 'msg'=>'数据不存在']));
			}

			// 计算升贴水
			$premiumDiscount = $this->_calculatePremiumDiscountByRules($chaoshi);

			die(json_encode([
				'code'=>1,
				'msg'=>'计算成功',
				'data'=>[
					'premium_discount' => $premiumDiscount,
					'original_jicha' => $chaoshi['jicha'],
					'calculated_jicha' => $chaoshi['jicha'] + $premiumDiscount
				]
			]));
		}else{
			die(json_encode(['code'=>0, 'msg'=>'参数错误']));
		}
	}

	/**
	 * 根据升贴水规则计算升贴水值
	 * @param array $chaoshi 超市数据
	 * @return float 升贴水值
	 */
	private function _calculatePremiumDiscountByRules($chaoshi){
		$totalPremiumDiscount = 0;

		// 获取启用的升贴水规则
		$rules = M('PremiumDiscountRule')->where(['is_enabled'=>1])->select();

		foreach($rules as $rule){
			// 获取规则详情
			$details = M('PremiumDiscountRuleDetail')
				->alias('d')
				->join('LEFT JOIN sd_premium_discount_condition_field f ON d.field_id = f.id')
				->field('d.*, f.field_code, f.field_type')
				->where(['d.rule_id'=>$rule['id']])
				->select();

			foreach($details as $detail){
				if($this->_checkCondition($chaoshi, $detail)){
					$totalPremiumDiscount += $detail['discount_value'];
				}
			}
		}

		return $totalPremiumDiscount;
	}

	/**
	 * 检查条件是否满足
	 * @param array $chaoshi 超市数据
	 * @param array $detail 规则详情
	 * @return bool
	 */
	private function _checkCondition($chaoshi, $detail){
		$fieldCode = $detail['field_code'];
		$fieldValue = isset($chaoshi[$fieldCode]) ? $chaoshi[$fieldCode] : null;

		if($fieldValue === null){
			return false;
		}

		$operator = $detail['operator'];

		if($detail['condition_type'] == 'range'){
			// 范围条件
			$minValue = $detail['min_value'];
			$maxValue = $detail['max_value'];

			if($operator == 'between'){
				return ($fieldValue >= $minValue && $fieldValue <= $maxValue);
			}
		}else{
			// 单值条件
			$conditionValue = $detail['condition_value'];

			switch($operator){
				case '>':
					return $fieldValue > $conditionValue;
				case '<':
					return $fieldValue < $conditionValue;
				case '=':
					return $fieldValue == $conditionValue;
				case '>=':
					return $fieldValue >= $conditionValue;
				case '<=':
					return $fieldValue <= $conditionValue;
				case '!=':
					return $fieldValue != $conditionValue;
				case 'in':
					$values = explode(',', $conditionValue);
					return in_array($fieldValue, $values);
			}
		}

		return false;
	}

	/**
	 * 升贴水计算器页面
	 */
	function premiumDiscountCalculator(){
		$this->display();
	}

	/**
	 * 根据表单数据计算升贴水
	 */
	function calculatePremiumDiscountByData(){
		$data = [
			'changdu' => I('post.changdu'),
			'qiangli' => I('post.qiangli'),
			'mazhi' => I('post.mazhi'),
			'huichaolv' => I('post.huichaolv'),
			'hanzalv' => I('post.hanzalv'),
			'zhengqidu' => I('post.zhengqidu'),
			'leixing' => I('post.leixing'),
			'jicha' => I('post.jicha', 0)
		];

		// 计算升贴水
		$result = $this->_calculatePremiumDiscountByRulesWithDetails($data);

		die(json_encode([
			'code'=>1,
			'msg'=>'计算成功',
			'data'=>[
				'premium_discount' => $result['total_premium_discount'],
				'original_jicha' => $data['jicha'],
				'calculated_jicha' => $data['jicha'] + $result['total_premium_discount'],
				'applied_rules' => $result['applied_rules']
			]
		]));
	}

	/**
	 * 根据升贴水规则计算升贴水值（带详情）
	 * @param array $data 数据
	 * @return array 计算结果
	 */
	private function _calculatePremiumDiscountByRulesWithDetails($data){
		$totalPremiumDiscount = 0;
		$appliedRules = [];

		// 获取启用的升贴水规则
		$rules = M('PremiumDiscountRule')->where(['is_enabled'=>1])->select();

		foreach($rules as $rule){
			// 获取规则详情
			$details = M('PremiumDiscountRuleDetail')
				->alias('d')
				->join('LEFT JOIN sd_premium_discount_condition_field f ON d.field_id = f.id')
				->field('d.*, f.field_code, f.field_type')
				->where(['d.rule_id'=>$rule['id']])
				->select();

			$ruleMatched = false;
			$ruleDiscount = 0;

			foreach($details as $detail){
				if($this->_checkCondition($data, $detail)){
					$ruleMatched = true;
					$ruleDiscount += $detail['discount_value'];
				}
			}

			if($ruleMatched){
				$totalPremiumDiscount += $ruleDiscount;
				$appliedRules[] = [
					'rule_name' => $rule['rule_name'],
					'discount_value' => $ruleDiscount
				];
			}
		}

		return [
			'total_premium_discount' => $totalPremiumDiscount,
			'applied_rules' => $appliedRules
		];
	}


}