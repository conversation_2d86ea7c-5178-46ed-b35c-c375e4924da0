<?php
namespace Back\Controller;
use Think\Controller;
use Vendor\Unlimit;
class ChaoshiController extends CommController {
	function _initialize(){
		parent::_initialize();
		if(session('uid_admin') == 1){
			$this->excels = M('Excel_file')->order('id desc')->select();	
		}else{
			$this->excels = M('Excel_file')->order('id desc')->where(['admin_uid'=>session('uid_admin')])->select();
		}
		

		$this->xls_leixing = C('xls_leixing');
		$this->xls_yanseji_pinji = C('xls_yanseji_pinji');
		$this->xls_niandu = C('xls_niandu');

		$this->xls_zhuangtai = C('xls_zhuangtai');

		$this->chandi = $changdi = M('Cat')->where(['pid'=>0])->order('id asc')->select();
		$this->def = $def = I('get.def') == '' ? $changdi[0]['id'] : I('get.def');
		$this->chandi_default = M('Cat')->where(['pid'=>$def])->order('id asc')->select();

		$this->jiaohuodi = M('Jiaohuodi')->where(['pid'=>0])->order('id asc')->select();


		if(I('get.cd') != '' && I('get.cd') !='|'){
			$cd = substr(I('get.cd') , 1, -1);
			$cd_arr = explode('|', $cd);
			$cd2 = [];
			foreach($cd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Cat')->where(['id'=>substr($v, 0 , -1)])->find();
				$cd2[$c['id']] =M('Cat')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($cd2);
			$this->cd2 = $cd2;
		}

		//孙产地
		$xx = $cd2;
		foreach($xx as $k=>$v){
			foreach($v as $k2=>$v2){
				$xx[$k][$k2]['children'] = M('Cat')->where(['pid'=>$v2['id']])->order('order_id desc, id asc')->select();
			}			
		}
		// print_r($xx);
		$this->xx = $xx;


		//jhd为父交货地，查询其子产地
		if(I('get.jhd') != '' && I('get.jhd') !='|'){
			$jhd = substr(I('get.jhd') , 1, -1);
			$jhd_arr = explode('|', $jhd);
			$jhd2 = [];
			foreach($jhd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Jiaohuodi')->where(['id'=>substr($v, 0 , -1)])->find();
				$jhd2[$c['id']] =M('Jiaohuodi')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($jhd2);
			$this->jhd2 = $jhd2;
		}
	}




	function runDelFile(){
		if($idarr = I('post.id')){
			foreach($idarr as $k=>$v){
				M('Chaoshi')->where(['file_id'=>$v])->delete();
				M('Excel_file')->delete($v);
			}

			die(json_encode(['code'=>1,'msg'=>'删除成功']));
		}
	}

	function delRec(){
		if(I('get.id')){
			M('Chaoshi')->where(['file_id'=>I("get.id")])->delete();
			M('Excel_file')->delete(I('get.id'));
			die(json_encode(['code'=>1,'msg'=>'删除成功']));
		}
			
	}


	public function runImportExcel(){
		$upload = new \Think\Upload();
		$upload->maxSize   =     50*1000*1000;// 2m
		$upload->exts      =     array('xls', 'xlsx');// 设置附件上传类型
		$upload->rootPath  =     './Attached/'; // 设置附件上传根目录
		$upload->savePath  =     'excel/'; // 设置附件上传（子）目录
		
		//上传文件
		$info   =   $upload->upload();
		
		if(!$info) {// 上传错误提示错误信息
			$tips = $upload->getError();
			die(json_encode(array('code'=>0 , 'msg'=>$tips)));
		}
	
		$filename = './Attached/'. $info['thumbs_hide']['savepath'].$info['thumbs_hide']['savename'];
		$origion_name = $info['thumbs_hide']['name'];
		//$filename = './Attached/excel/2016-06-02/574f7132176be.xls';
		
		//清空旧数据
		// M('Company')->where(array('`add_time`>0'))->delete();
		
		require_once './Excel/PHPExcel.php';//包含excel入口
		require_once './Excel/PHPExcel/IOFactory.php';
		require_once './Excel/PHPExcel/Reader/Excel5.php';
		
		if(substr($filename, strripos($filename, '.'))=='.xls'){
			$objReader = \PHPExcel_IOFactory::createReader('Excel5');
		}else{
			$objReader = \PHPExcel_IOFactory::createReader('Excel2007');//use excel2007 for 2007 format
		}
				
		$objPHPExcel = $objReader->load($filename); //$filename可以是上传的文件，或者是指定的文件
		$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);

		// //验证工厂、仓库是否添加
		// $gc_tips =['------------------工厂-------------------'];
		// $ck_tips =['------------------仓库-------------------'];
		// $ph_tips =['--------------批号，级差----------------'];
		// foreach($sheetData as $k=> $v){
		// 	if($k>1  && $v['B']!=''){
		// 		//检查工厂重复
		// 		if(count($gc_tips)<21){
		// 			if(!M('Gongchang')->where(['title'=>$v['M']])->find()){
		// 				$gc_tips[] = $k.'行工厂"'.$v['M'].'"不存在';
		// 			}
		// 		}

		// 		//检查仓库重复
		// 		if(count($ck_tips)<21){
		// 			if(!M('Cangku')->where(['title'=>$v['N']])->find()){
		// 				$ck_tips[] = $k.'行仓库"'.$v['N'].'"不存在';
		// 			}
		// 		}

		// 		// 检查批号，级差
		// 		if(count($ph_tips)<21){
		// 			if(M('Chaoshi')->where(['pihao_kunkao'=>$v['B'], 'jicha'=>$v['O'], 'jicha2'=>$v['S']])->find()){
		// 				$ph_tips[] = $k.'行批号，级差已存在，不允许重复';
		// 			}
		// 		}
		// 	}

		// }

		// // print_r($gc_tips);
		// // print_r($ck_tips);
		// // print_r($ph_tips);

		// $tips_all = array_merge($gc_tips, $ck_tips, $ph_tips);
		// //三行提示
		// if(count($tips_all)>3){
		// 	die(json_encode(['code'=>0, 'msg'=>implode(PHP_EOL, $tips_all), 'id'=>I('get.id')]));
		// }

		//数据处理
		foreach($sheetData as $k=> $v){
			if($k>1  && $v['B']!=''){
				if($v['D']){
					//（白棉三级:98.4%;淡点污棉一级:1.6%;）中文括号
					preg_match_all("/（(.*?)）/ism",$v['D'], $result); 
					//result[1][0]=> 白棉三级:98.4%;淡点污棉一级:1.6%;
					// print_r($result[1][0]);
					// 去掉最后分号再分割 ，否则多一项
					$after_cut = substr($result[1][0], 0, -1);
					// print_r($after_cut);
					//分割$after_cut后的字串=> 白棉三级:98.4%;淡点污棉一级:1.6%
					$arr = explode(';', $after_cut);
					// Array
					// (
					//     [0] => 白棉二级:4.3%
					//     [1] => 白棉三级:81.7%
					//     [2] => 白棉四级:14.0%
					// )
					// print_r($arr);
					$bm123 = 0;
					$bm45 = 0;
					$ddw123 = 0;
					$dhr123 = 0;
					$hr12 = 0;
					foreach($arr as $x=>$y){
						//白棉二级:4.3%
						$temp = explode(':', $y);
						if($temp[0] == '白棉一级' || $temp[0] == '白棉二级' || $temp[0] == '白棉三级'){
							$bm123 += $temp[1];
						}else if($temp[0] == '白棉四级' || $temp[0] == '白棉五级'){
							$bm45 += $temp[1];
						}else if($temp[0] == '淡点污棉一级' || $temp[0] == '淡点污棉二级' || $temp[0] == '淡点污棉三级'){
							$ddw123 += $temp[1];
						}else if($temp[0] == '淡黄染棉一级' || $temp[0] == '淡黄染棉二级' || $temp[0] == '淡黄染棉三级'){
							$dhr123 += $temp[1];
						}else if($temp[0] == '黄染棉一级' || $temp[0] == '黄染棉二级'){
							$hr12 += $temp[1];
						}
					}

					$sheetData[$k]['bm123'] = $bm123;
					$sheetData[$k]['bm45'] = $bm45;
					$sheetData[$k]['ddw123'] = $ddw123;
					$sheetData[$k]['dhr123'] = $dhr123;
					$sheetData[$k]['hr12'] = $hr12;
				}
				
			}
		}

		// print_r($sheetData);
		// die;
		//文件列表
		$lastid = M('Excel_file')->add(['title'=>$origion_name, 'add_time'=>time(), 'admin_uid'=>session('uid_admin')]);
		// 入库

		foreach($sheetData as $k=> $v){
				//去除表头，从第二行开始
				if($k>1  && $v['B']!=''){

					$res = M('Gongchang')->where(['title'=>$v['N']])->find();
					$res2 = M('Cangku')->where(['title'=>$v['O']])->find();

					$leixing = str_replace('皮辊细绒棉','手摘棉', $v['C']);
					$leixing = str_replace('锯齿细绒棉','手摘棉',  $leixing);
					$leixing = str_replace('锯齿机采棉', '机采棉', $leixing);
					$arr= array(
						'xvhao'=>$v['A'],
						'pihao_kunkao'=>$v['B'],
						'leixing'=> $leixing,
						'yanseji_pinji'=>$v['D'],
						'mazhi'=>$v['E'],
						'changdu'=>$v['F'],
						'qiangli'=>$v['G'],
						'hanzalv'=>$v['H'],
						'huichaolv'=>$v['I'],
						'gongzhong'=>$v['J'],
						'maozhaong'=>$v['K'],
						'zhengqidu'=>number_format($v['L'],1),
						'ygzl'=>$v['M'],
						'jiagongchang'=>$v['N'],
						'cangchumingcheng'=>$v['O'],
						'jicha'=>$v['P'],
						'beizhu'=>$v['Q'],
						'dianjiaheyue'=>$v['R'],
						'baoshu'=>$v['S'],

						'bm123'=>$v['bm123'],
						'bm45'=>$v['bm45'],
						'ddw123'=>$v['ddw123'],
						'dhr123'=>$v['dhr123'],
						'hr12'=>$v['hr12'],
						'path'=>$res['level1'].'-'.$res['level2'].'-'.$res['level3'].'-',
						'path_cangku'=>$res2['level1'].'-'.$res2['level2'].'-'.$res2['level3'].'-',
						'is_hot'=>$res['is_hot'],
						'jiaohuodi'=>$res['jiaohuodi'],
						'add_time'=>time(),
						'file_id'=>$lastid,
						'year'=>substr($v['B'], 5,2),
						'uid'=>session('uid_admin'),
					);

					// 计算升贴水
					$premiumDiscount = $this->_calculatePremiumDiscountByRules($arr);
					$arr['premium_discount'] = $premiumDiscount;
					$arr['calculated_jicha'] = $arr['jicha'] + $premiumDiscount;

					M('Chaoshi')->add($arr);
				}
		}

		die(json_encode(array('code'=>1 , 'msg'=>'导入成功', 'url'=>$filename, 'id'=>I('get.id'))));

	}


	public function toExcel(){
		$where = '`id` > 0';
		// if(I('get.key')){
		// 	$where .= ' and (`order_no` like "%'.I('get.key').'%" or `address_name` like "%'.I('get.key').'%" or `address_mobile` like "%'.I('get.key').'%")';
		// }

		// if(I('get.start_time')){
		// 	$where .= ' and `add_time` >'.strtotime(I('get.start_time'));
		// }

		// if(I('get.end_time')){
		// 	$where .= ' and `add_time` <'.strtotime(I('get.end_time'));
		// }


		// print_r($where);

		$count =  M('Chaoshi')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order('pay_time desc, add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){

			$lists[$k]['uid_text']= M('user')->where(['id'=>$v['uid']])->getField('nickName');
			$lists[$k]['coupon'] = M('Coupon')->where(['id'=>$v['coupon_id']])->find();
			$lists[$k]['xiangxi']= M('Order')->where(['order_no'=>$v['order_no']])->select();
		}

		// print_r($lists);
		// $this->lists = $lists;

		header("Content-Type: application/vnd.ms-excel;charset=UTF-8");
		header("Content-Disposition: attachment; filename=".iconv('utf-8', 'gb2312', '订单管理_'). date('Y-m-d').".xls");
		header("Pragma: no-cache");
		header("Expires: 0");
		echo("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>");
		echo('<table border=1>');
		echo('<tr><th>订单号</th><th>价格</th><th>优惠券</th><th>用户名</th><th>收件人及地址</th><th>下单时间</th><th>支付时间</th></tr>');
		foreach ($lists as $k=>$v){
			echo('<tr>');
			echo('<td align="center">&nbsp;'.$v['order_no'].'</td>');
			echo('<td align="center">'.$v['price'].'</td>');
			if($v['coupon']['title'] != ''){
				echo('<td align="center">'.$v['coupon']['title'] .'('.$v['coupon']['price'] .')</td>');
			}else{
				echo('<td align="center">&nbsp;</td>');
			}
			
			echo('<td align="center">'.$v['uid_text'].'</td>');
			echo('<td align="center">'.$v['address_name'] .'-'.$v['address_mobile'].'<br />'.$v['address_province'].$v['address_city'].$v['address_county'].$v['address_address'].'</td>');
			echo('<td align="center">&nbsp; '.date('Y-m-d H:i', $v['add_time']).'</td>');
			if($v['pay_time']  != 0){
				echo('<td align="center">&nbsp; '.date('Y-m-d H:i', $v['pay_time']).'</td>');
			}else{
				echo('<td align="center">&nbsp;</td>');
			}
			echo('</tr>');
			echo('<tr><td></td>');
			echo('<td colspan="6">');
			foreach($v['xiangxi'] as $k2=>$v2){
				echo($v2['ke_title'].'('.$v2['ke_price'].')<br />');
			}
			echo('</td></tr>');
		}
		echo('</table>');
	}

	function getChild(){
		if(I('get.id')){
			$lists = M('Cat')->where(['pid'=>I('get.id')])->order('order_id desc, id asc')->select();
			die(json_encode($lists));
		}
		
	}


	function index(){
		if(I('get.pihao')){
			$where = ' `pihao_kunkao` = "'.I('get.pihao').'" ';
		}else{
			$where = '`id` > 0 ';
		}
		

//搜索地区
$cd = substr(I('get.cd'), 1, -1);
$cd2 = substr(I('get.cd2'), 1, -1);
$cd3 = substr(I('get.cd3'), 1, -1);

$cda = explode('|', $cd);
$cda2 = explode('|', $cd2);
$cda3 = explode('|', $cd3);
// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含于第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($cda as $k=>$v){
	foreach($cda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($cda[$k]);
			break;
		}
	}
}

foreach($cda2 as $k2=>$v2){
	foreach($cda3 as $k3=>$v3){
		if(strrpos($v3, $v2) !== false){
			// array_splice($cda2, $k2, 1);
			unset($cda2[$k2]);
			break;
		}
	}
}

// print_r('------------');
// print_r($cda);
// print_r($cda2);
// print_r($cda3);

$lasta = array_merge($cda, $cda2,$cda3);
// print_r($lasta);
		
		if($lasta){
			$sql = '';
			foreach($lasta as $k=>$v){
				if($v){
					$sql .= '`path`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}
		

//交货地开始
$jhd = substr(I('get.jhd'), 1, -1);
$jhd2 = substr(I('get.jhd2'), 1, -1);


$jhda = explode('|', $jhd);
$jhda2 = explode('|', $jhd2);

// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($jhda as $k=>$v){
  foreach($jhda2 as $k2=>$v2){
    if(strrpos($v2, $v) !== false){
      // array_splice($cda, $k, 1);
      unset($jhda[$k]);
      break;
    }
  }
}


$lasta2 = array_merge($jhda, $jhda2);
// print_r($lasta2);
    
    if($lasta2){
      $sql = '';
      foreach($lasta2 as $k=>$v){
        if($v){
          $sql .= '`path_cangku`  like "%'.$v.'%"  or ';
        }
      }

      if($sql){
        //去掉最后一个" or"
        $sql = substr($sql, 0, -3);
        $sql =  ' and ('.$sql.')';
      }

      $where .= $sql;
      // print_r($where);
    }


		if(I('get.leixing') !='|'&& I('get.leixing') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.leixing'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`leixing`  like "%'.$v.'%"  or ';
				}
			}

			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			//`id` > 0 and (`leixing` like "%手摘棉%" or `leixing` like "%机采棉%" )
			// print_r($where);
		}

		if(I('get.pihao_kunkao') !='|' && I('get.pihao_kunkao') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.pihao_kunkao'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`year`  = "'.substr($v, 2, 2).'"  or ';
				}
			}
			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.changdu_from')!=''){
			$changdu_from = I('get.changdu_from') == 25 ? 0 : I('get.changdu_from');
			$where .= ' and `changdu` >= '.$changdu_from;
		}

		if(I('get.changdu_to')!=''){
			$changdu_to = I('get.changdu_to') == 32 ? 50 : I('get.changdu_to');
			$where .= ' and `changdu` <= '.$changdu_to;
		}

		
		if(I('get.mazhi_from')!=''){
			$mazhi_from = I('get.mazhi_from') == 2.5 ? 0 : I('get.mazhi_from');
			$where .= ' and `mazhi` >= '.$mazhi_from;
		}

		if(I('get.mazhi_to')!=''){
			$mazhi_to = I('get.mazhi_to') == 5.5 ? 10 : I('get.mazhi_to');
			$where .= ' and `mazhi` <= '.$mazhi_to;
		}

		if(I('get.qiangli_from')!=''){
			$qiangli_from = I('get.qiangli_from') == 25 ? 0 : I('get.qiangli_from');
			$where .= ' and `qiangli` >= '.$qiangli_from;
		}

		if(I('get.qiangli_to')!=''){
			$qiangli_to = I('get.qiangli_to') == 32 ? 50 : I('get.qiangli_to');
			$where .= ' and `qiangli` <= '.$qiangli_to;
		}

		if(I('get.huichaolv_from')!=''){
			$huichaolv_from = I('get.huichaolv_from') == 0 ? 0 : I('get.huichaolv_from');
			$where .= ' and `huichaolv` >= '.$huichaolv_from;
		}

		if(I('get.huichaolv_to')!=''){
			$huichaolv_to = I('get.huichaolv_to') == 10 ? 25 : I('get.huichaolv_to');
			$where .= ' and `huichaolv` <= '.$huichaolv_to;
		}

		if(I('get.zhengqidu_from')!=''){
			$zhengqidu_from = I('get.zhengqidu_from') == 77 ? 0 : I('get.zhengqidu_from');
			$where .= ' and `zhengqidu` >= '.$zhengqidu_from;
		}

		if(I('get.zhengqidu_to')!=''){
			$zhengqidu_to = I('get.zhengqidu_to') == 90 ? 100 : I('get.zhengqidu_to');
			$where .= ' and `zhengqidu` <= '.$zhengqidu_to;
		}

		if(I('get.hanzalv_from')!=''){
			$hanzalv_from = I('get.hanzalv_from') == 0 ? 0 : I('get.hanzalv_from');
			$where .= ' and `hanzalv` >= '.$hanzalv_from;
		}

		if(I('get.hanzalv_to')!=''){
			$hanzalv_to = I('get.hanzalv_to') == 6 ? 100 : I('get.hanzalv_to');
			$where .= ' and `hanzalv` <= '.$hanzalv_to;
		}


		if(I('get.bm123_from')!=''){
			$where .= ' and `bm123` >= '.I('get.bm123_from');
		}

		if(I('get.bm123_to')!=''){
			$where .= ' and `bm123` <= '.I('get.bm123_to');
		}



		if(I('get.bm45_from')!=''){
			$where .= ' and `bm45` >= '.I('get.bm45_from');
		}

		if(I('get.bm45_to')!=''){
			$where .= ' and `bm45` <= '.I('get.bm45_to');
		}


		if(I('get.ddw123_from')!=''){
			$where .= ' and `ddw123` >= '.I('get.ddw123_from');
		}

		if(I('get.ddw123_to')!=''){
			$where .= ' and `ddw123` <= '.I('get.ddw123_to');
		}	



		if(I('get.dhr123_from')!=''){
			$where .= ' and `dhr123` >= '.I('get.dhr123_from');
		}

		if(I('get.dhr123_to')!=''){
			$where .= ' and `dhr123` <= '.I('get.dhr123_to');
		}



		if(I('get.hr12_from')!=''){
			$where .= ' and `hr12` >= '.I('get.hr12_from');
		}

		if(I('get.hr12_to')!=''){
			$where .= ' and `hr12` <= '.I('get.hr12_to');
		}



		if(I('get.jiaohuodi')!=''){
			$where .= ' and `jiaohuodi` = "'.I('get.jiaohuodi').'"';
		}


		if(I('get.hot') == 1){
			$where .= ' and `is_hot` = 1';
		}


		if(I('get.jiagongchang')!=''){
			$where .= ' and `jiagongchang` like "%'.I('get.jiagongchang').'%"';
		}


		if(I('get.cangchumingcheng')!=''){
			$where .= ' and `cangchumingcheng` like "%'.I('get.cangchumingcheng').'%"';
		}

		if(I('get.dan')!=''){
			$where .= ' and `dan_type` = "'.I('get.dan').'"';
		}


		// print_r($this->session_admin);
		if($this->session_admin['type']==1){
			$where.=' and `uid` = '.$this->session_admin['id'];
		}


		

		if(I('get.zuiyou')){
			$order = '`jicha` asc';
		}else if(I('get.xiangtong')){
			$order = '`'.I('get.xiangtong').'` asc';
		}else if(I('get.jicha')){
			$order = '`jicha` asc';
		}else{
			$order = ' id asc ';
		}

		// print_r($where);
		// die;
		
		$count =  M('Chaoshi')->where($where)->count();

		if(!empty(I('get.zuiyou')) && I('get.zuiyou') <= $count){
			$count = I('get.zuiyou');
		}

		$this->count = $count;

		// echo $this->count;
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order($order)->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			if($v['path']){
				$path = explode('-', substr($v['path'], 0 , -1));
				$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[2]])->getField('name');
			}

			// 计算升贴水（如果还没有计算过）
			if(!isset($v['premium_discount']) || $v['premium_discount'] === null){
				$premiumDiscount = $this->_calculatePremiumDiscountByRules($v);
				$lists[$k]['premium_discount'] = $premiumDiscount;
				$lists[$k]['calculated_jicha'] = $v['jicha'] + $premiumDiscount;
			} else {
				$lists[$k]['premium_discount'] = $v['premium_discount'];
				$lists[$k]['calculated_jicha'] = $v['calculated_jicha'] ?: ($v['jicha'] + $v['premium_discount']);
			}

		}
		$this->lists = $lists;
		// print_r(M('Chaoshi')->_sql());
		$this->display();
		
	}


	function changeZhuangtai(){
		if(I('get.zhuangtai')){
			M('Chaoshi')->where(array('id'=>I('get.id')))->save(array('zhuangtai'=>I('get.zhuangtai')));
			die(json_encode(array('code'=>1)));
		}
	}
	public function add(){
		$this->display('add_tit_cont');
		
		
	}
	public function runAdd(){
		$a = M('Chaoshi');
		if($a->create()){
				$a->add_time = I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
				$a->pub_date = strtotime(I('post.pub_date'));
				$a->content = $_POST['content'];
				$a->order_id = 100;

				if($a->add()){
					//$this->show($a->_sql());
					die(json_encode(array('code'=>1)));
				}
		}
	}
	public function edit(){
		$this->res = $res = M('Chaoshi')->find(I('get.id'));

		$this->display('edit_tit_cont');
	}
	public function runEdit(){
		$a = M('Chaoshi');
		if($a->create()){
			$a->add_time =I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
			$a->pub_date = strtotime(I('post.pub_date'));
			$a->content = $_POST['content'];

			$a->save();
			// $this->redirect('index', array('cid'=>I('post.cid'),'type'=>I('post.type')));
			die(json_encode(array('code'=>1)));
		}
	}

	function changeZhuangtaiAll(){
		if(I('post.id')){
			foreach(I('post.id') as $k=>$v){
				M('Chaoshi')->where(['id'=>$v])->save(['zhuangtai'=>I('post.zhuangtai_text')]);
			}
			die(json_encode(array('code'=>1)));
		}
	}

	function delAll(){
		// print_r($_POST);
		if(I('post.id')){

			foreach(I('post.id') as $k=>$v){
				$res = M('Chaoshi')->find($v);
				//删除图片
				if($res['thumbs']!=''){
					@unlink(realpath('.').'/Attached/'.$res['thumbs']);
				}

				M('Favor')->where(['favor_id'=>$res['id']])->delete();
				M('Chaoshi')->delete($res['id']);
			}


			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('Chaoshi')->find(I('get.id'));
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}

		M('Favor')->where(['favor_id'=>I('get.id')])->delete();
		if(M('Chaoshi')->delete(I('get.id'))){
			die(json_encode(array('code'=>1)));
		}
	}
	public function runOrder(){
		M('Chaoshi')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}
	public function runRecom(){
		$res = M('Chaoshi')->find(I('get.id'));
		if($res['is_recom']==1){
			M('Chaoshi')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('Chaoshi')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}

	/**
	 * 批量更新升贴水
	 */
	function updatePremiumDiscountBatch(){
		if(I('post.id')){
			$successCount = 0;
			$errorCount = 0;

			foreach(I('post.id') as $k=>$v){
				try {
					// 获取记录数据
					$record = M('Chaoshi')->where(['id'=>$v])->find();
					if($record){
						// 计算升贴水
						$premiumDiscount = $this->_calculatePremiumDiscountByRules($record);
						$calculatedJicha = $record['jicha'] + $premiumDiscount;

						// 更新数据库
						M('Chaoshi')->where(['id'=>$v])->save([
							'premium_discount' => $premiumDiscount,
							'calculated_jicha' => $calculatedJicha
						]);
						$successCount++;
					}
				} catch (Exception $e) {
					$errorCount++;
				}
			}

			die(json_encode(array(
				'code'=>1,
				'msg'=>"升贴水更新完成！成功：{$successCount}条，失败：{$errorCount}条"
			)));
		} else {
			die(json_encode(array('code'=>0, 'msg'=>'请选择要更新的记录')));
		}
	}

	/**
	 * 单条记录更新升贴水
	 */
	function updatePremiumDiscountSingle(){
		$id = I('get.id');
		if($id){
			try {
				// 获取记录数据
				$record = M('Chaoshi')->where(['id'=>$id])->find();
				if($record){
					// 计算升贴水
					$premiumDiscount = $this->_calculatePremiumDiscountByRules($record);
					$calculatedJicha = $record['jicha'] + $premiumDiscount;

					// 更新数据库
					M('Chaoshi')->where(['id'=>$id])->save([
						'premium_discount' => $premiumDiscount,
						'calculated_jicha' => $calculatedJicha
					]);

					die(json_encode(array(
						'code'=>1,
						'msg'=>'升贴水更新成功',
						'data'=>[
							'premium_discount' => $premiumDiscount,
							'calculated_jicha' => $calculatedJicha
						]
					)));
				} else {
					die(json_encode(array('code'=>0, 'msg'=>'记录不存在')));
				}
			} catch (Exception $e) {
				die(json_encode(array('code'=>0, 'msg'=>'更新失败：'.$e->getMessage())));
			}
		} else {
			die(json_encode(array('code'=>0, 'msg'=>'参数错误')));
		}
	}

	/**
	 * 根据升贴水规则计算升贴水值
	 * @param array $data 棉花数据
	 * @return float 升贴水值
	 */
	private function _calculatePremiumDiscountByRules($data){
		$totalPremiumDiscount = 0;

		// 获取启用的升贴水规则
		$rules = M('PremiumDiscountRule')->where(['is_enabled'=>1])->select();

		if(!$rules) {
			return 0; // 如果没有规则，返回0
		}

		foreach($rules as $rule){
			// 获取规则详情
			$details = M('PremiumDiscountRuleDetail')
				->alias('d')
				->join('LEFT JOIN sd_premium_discount_condition_field f ON d.field_id = f.id')
				->field('d.*, f.field_code, f.field_type')
				->where(['d.rule_id'=>$rule['id']])
				->select();

			foreach($details as $detail){
				if($this->_checkCondition($data, $detail)){
					$totalPremiumDiscount += $detail['discount_value'];
				}
			}
		}

		return $totalPremiumDiscount;
	}

	/**
	 * 检查条件是否满足
	 * @param array $data 棉花数据
	 * @param array $detail 规则详情
	 * @return bool
	 */
	private function _checkCondition($data, $detail){
		$fieldCode = $detail['field_code'];
		$fieldValue = isset($data[$fieldCode]) ? $data[$fieldCode] : null;

		if($fieldValue === null || $fieldValue === ''){
			return false;
		}

		$operator = $detail['operator'];

		if($detail['condition_type'] == 'range'){
			// 范围条件
			$minValue = $detail['min_value'];
			$maxValue = $detail['max_value'];

			if($operator == 'between'){
				return ($fieldValue >= $minValue && $fieldValue <= $maxValue);
			}
		}else{
			// 单值条件
			$conditionValue = $detail['condition_value'];

			switch($operator){
				case '>':
					return $fieldValue > $conditionValue;
				case '<':
					return $fieldValue < $conditionValue;
				case '=':
					return $fieldValue == $conditionValue;
				case '>=':
					return $fieldValue >= $conditionValue;
				case '<=':
					return $fieldValue <= $conditionValue;
				case '!=':
					return $fieldValue != $conditionValue;
				case 'in':
					$values = explode(',', $conditionValue);
					return in_array($fieldValue, $values);
			}
		}

		return false;
	}

}