-- 升贴水规则示例数据（简化版）

-- 创建升贴水规则相关表（如果不存在）
CREATE TABLE IF NOT EXISTS `sd_premium_discount_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水规则表';

CREATE TABLE IF NOT EXISTS `sd_premium_discount_condition_field` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field_code` varchar(50) NOT NULL COMMENT '字段代码',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_type` varchar(20) DEFAULT 'number' COMMENT '字段类型',
  `field_unit` varchar(20) DEFAULT '' COMMENT '字段单位',
  `is_range` tinyint(1) DEFAULT 0 COMMENT '是否支持范围条件',
  `select_options` text COMMENT '选择项（JSON格式）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_code` (`field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水条件字段表';

CREATE TABLE IF NOT EXISTS `sd_premium_discount_rule_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_id` int(11) NOT NULL COMMENT '规则ID',
  `field_id` int(11) NOT NULL COMMENT '字段ID',
  `condition_type` varchar(20) DEFAULT 'single' COMMENT '条件类型：single单值，range范围',
  `operator` varchar(10) NOT NULL COMMENT '操作符：>,<,=,>=,<=,!=,between,in',
  `condition_value` varchar(100) DEFAULT NULL COMMENT '条件值',
  `min_value` decimal(10,2) DEFAULT NULL COMMENT '最小值（范围条件）',
  `max_value` decimal(10,2) DEFAULT NULL COMMENT '最大值（范围条件）',
  `discount_value` decimal(10,2) NOT NULL COMMENT '升贴水值',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_field_id` (`field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='升贴水规则详情表';

-- 清空现有数据
TRUNCATE TABLE `sd_premium_discount_rule_detail`;
TRUNCATE TABLE `sd_premium_discount_rule`;
TRUNCATE TABLE `sd_premium_discount_condition_field`;

-- 插入条件字段
INSERT INTO `sd_premium_discount_condition_field` (`field_code`, `field_name`, `field_type`, `field_unit`, `is_range`, `sort_order`) VALUES
('changdu', '长度', 'number', 'mm', 1, 10),
('qiangli', '强力', 'number', 'cN/tex', 1, 20),
('mazhi', '马值', 'number', '', 1, 30),
('huichaolv', '回潮率', 'number', '%', 1, 40),
('hanzalv', '含杂率', 'number', '%', 1, 50),
('zhengqidu', '整齐度', 'number', '%', 1, 60),
('leixing', '类型', 'select', '', 0, 70);

-- 更新类型字段的选择项
UPDATE `sd_premium_discount_condition_field` SET `select_options` = '["手摘棉","机采棉"]' WHERE `field_code` = 'leixing';

-- 插入升贴水规则
INSERT INTO `sd_premium_discount_rule` (`rule_name`, `is_enabled`) VALUES
('长度升贴水规则', 1),
('强力升贴水规则', 1),
('马值升贴水规则', 1),
('回潮率升贴水规则', 1),
('含杂率升贴水规则', 1),
('整齐度升贴水规则', 1),
('类型升贴水规则', 1);

-- 长度升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `min_value`, `max_value`, `discount_value`) 
SELECT r.id, f.id, 'range', 'between', '29', '50', 50.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `min_value`, `max_value`, `discount_value`) 
SELECT r.id, f.id, 'range', 'between', '27', '28.9', 20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '26', -30.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '长度升贴水规则' AND f.field_code = 'changdu';

-- 强力升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '30', 40.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '强力升贴水规则' AND f.field_code = 'qiangli';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '25', -25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '强力升贴水规则' AND f.field_code = 'qiangli';

-- 马值升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '4.5', 30.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '马值升贴水规则' AND f.field_code = 'mazhi';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '3.5', -20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '马值升贴水规则' AND f.field_code = 'mazhi';

-- 回潮率升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>', '8.5', -15.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '回潮率升贴水规则' AND f.field_code = 'huichaolv';

-- 含杂率升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>', '3', -25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '含杂率升贴水规则' AND f.field_code = 'hanzalv';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<=', '1', 20.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '含杂率升贴水规则' AND f.field_code = 'hanzalv';

-- 整齐度升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '>=', '85', 25.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '整齐度升贴水规则' AND f.field_code = 'zhengqidu';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '<', '80', -15.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '整齐度升贴水规则' AND f.field_code = 'zhengqidu';

-- 类型升贴水规则详情
INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '=', '手摘棉', 35.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '类型升贴水规则' AND f.field_code = 'leixing';

INSERT INTO `sd_premium_discount_rule_detail` (`rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `discount_value`) 
SELECT r.id, f.id, 'single', '=', '机采棉', -10.00
FROM `sd_premium_discount_rule` r, `sd_premium_discount_condition_field` f 
WHERE r.rule_name = '类型升贴水规则' AND f.field_code = 'leixing';

-- 查看插入的数据
SELECT '升贴水规则' as '表名', COUNT(*) as '记录数' FROM `sd_premium_discount_rule`
UNION ALL
SELECT '条件字段', COUNT(*) FROM `sd_premium_discount_condition_field`
UNION ALL
SELECT '规则详情', COUNT(*) FROM `sd_premium_discount_rule_detail`;
