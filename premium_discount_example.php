<?php
/**
 * 升贴水计算示例
 * 
 * 使用方法：
 * 1. 首先执行 premium_discount.sql 创建数据库表结构
 * 2. 然后执行 premium_discount_sample_data.sql 插入示例数据
 * 3. 运行此脚本查看升贴水计算示例
 */

// 模拟棉花数据
$cottonSamples = [
    [
        'name' => '优质手摘棉',
        'changdu' => 29.5,      // 长度 29.5mm (升水 +50)
        'qiangli' => 31.2,      // 强力 31.2 cN/tex (升水 +40)
        'mazhi' => 4.8,         // 马值 4.8 (升水 +30)
        'huichaolv' => 7.2,     // 回潮率 7.2% (无升贴水)
        'hanzalv' => 0.8,       // 含杂率 0.8% (升水 +20)
        'zhengqidu' => 86.5,    // 整齐度 86.5% (升水 +25)
        'leixing' => '手摘棉',   // 手摘棉 (升水 +35)
        'jicha' => 100          // 基差 100
    ],
    [
        'name' => '普通机采棉',
        'changdu' => 26.8,      // 长度 26.8mm (无升贴水)
        'qiangli' => 24.5,      // 强力 24.5 cN/tex (贴水 -25)
        'mazhi' => 3.2,         // 马值 3.2 (贴水 -20)
        'huichaolv' => 9.1,     // 回潮率 9.1% (贴水 -15)
        'hanzalv' => 3.5,       // 含杂率 3.5% (贴水 -25)
        'zhengqidu' => 78.2,    // 整齐度 78.2% (贴水 -15)
        'leixing' => '机采棉',   // 机采棉 (贴水 -10)
        'jicha' => 50           // 基差 50
    ],
    [
        'name' => '中等品质棉花',
        'changdu' => 27.5,      // 长度 27.5mm (升水 +20)
        'qiangli' => 28.0,      // 强力 28.0 cN/tex (无升贴水)
        'mazhi' => 4.0,         // 马值 4.0 (无升贴水)
        'huichaolv' => 8.0,     // 回潮率 8.0% (无升贴水)
        'hanzalv' => 2.0,       // 含杂率 2.0% (无升贴水)
        'zhengqidu' => 82.0,    // 整齐度 82.0% (无升贴水)
        'leixing' => '手摘棉',   // 手摘棉 (升水 +35)
        'jicha' => 75           // 基差 75
    ]
];

/**
 * 升贴水规则定义
 */
$premiumDiscountRules = [
    '长度升贴水规则' => [
        ['condition' => '>=29', 'discount' => 50],
        ['condition' => '27-28.9', 'discount' => 20],
        ['condition' => '<26', 'discount' => -30]
    ],
    '强力升贴水规则' => [
        ['condition' => '>=30', 'discount' => 40],
        ['condition' => '<25', 'discount' => -25]
    ],
    '马值升贴水规则' => [
        ['condition' => '>=4.5', 'discount' => 30],
        ['condition' => '<3.5', 'discount' => -20]
    ],
    '回潮率升贴水规则' => [
        ['condition' => '>8.5', 'discount' => -15]
    ],
    '含杂率升贴水规则' => [
        ['condition' => '>3', 'discount' => -25],
        ['condition' => '<=1', 'discount' => 20]
    ],
    '整齐度升贴水规则' => [
        ['condition' => '>=85', 'discount' => 25],
        ['condition' => '<80', 'discount' => -15]
    ],
    '类型升贴水规则' => [
        ['condition' => '=手摘棉', 'discount' => 35],
        ['condition' => '=机采棉', 'discount' => -10]
    ]
];

/**
 * 计算升贴水
 */
function calculatePremiumDiscount($cotton, $rules) {
    $totalDiscount = 0;
    $appliedRules = [];
    
    // 长度规则
    if ($cotton['changdu'] >= 29) {
        $totalDiscount += 50;
        $appliedRules[] = ['rule' => '长度升贴水', 'condition' => '>=29mm', 'discount' => 50];
    } elseif ($cotton['changdu'] >= 27 && $cotton['changdu'] <= 28.9) {
        $totalDiscount += 20;
        $appliedRules[] = ['rule' => '长度升贴水', 'condition' => '27-28.9mm', 'discount' => 20];
    } elseif ($cotton['changdu'] < 26) {
        $totalDiscount -= 30;
        $appliedRules[] = ['rule' => '长度升贴水', 'condition' => '<26mm', 'discount' => -30];
    }
    
    // 强力规则
    if ($cotton['qiangli'] >= 30) {
        $totalDiscount += 40;
        $appliedRules[] = ['rule' => '强力升贴水', 'condition' => '>=30 cN/tex', 'discount' => 40];
    } elseif ($cotton['qiangli'] < 25) {
        $totalDiscount -= 25;
        $appliedRules[] = ['rule' => '强力升贴水', 'condition' => '<25 cN/tex', 'discount' => -25];
    }
    
    // 马值规则
    if ($cotton['mazhi'] >= 4.5) {
        $totalDiscount += 30;
        $appliedRules[] = ['rule' => '马值升贴水', 'condition' => '>=4.5', 'discount' => 30];
    } elseif ($cotton['mazhi'] < 3.5) {
        $totalDiscount -= 20;
        $appliedRules[] = ['rule' => '马值升贴水', 'condition' => '<3.5', 'discount' => -20];
    }
    
    // 回潮率规则
    if ($cotton['huichaolv'] > 8.5) {
        $totalDiscount -= 15;
        $appliedRules[] = ['rule' => '回潮率升贴水', 'condition' => '>8.5%', 'discount' => -15];
    }
    
    // 含杂率规则
    if ($cotton['hanzalv'] > 3) {
        $totalDiscount -= 25;
        $appliedRules[] = ['rule' => '含杂率升贴水', 'condition' => '>3%', 'discount' => -25];
    } elseif ($cotton['hanzalv'] <= 1) {
        $totalDiscount += 20;
        $appliedRules[] = ['rule' => '含杂率升贴水', 'condition' => '<=1%', 'discount' => 20];
    }
    
    // 整齐度规则
    if ($cotton['zhengqidu'] >= 85) {
        $totalDiscount += 25;
        $appliedRules[] = ['rule' => '整齐度升贴水', 'condition' => '>=85%', 'discount' => 25];
    } elseif ($cotton['zhengqidu'] < 80) {
        $totalDiscount -= 15;
        $appliedRules[] = ['rule' => '整齐度升贴水', 'condition' => '<80%', 'discount' => -15];
    }
    
    // 类型规则
    if ($cotton['leixing'] == '手摘棉') {
        $totalDiscount += 35;
        $appliedRules[] = ['rule' => '类型升贴水', 'condition' => '手摘棉', 'discount' => 35];
    } elseif ($cotton['leixing'] == '机采棉') {
        $totalDiscount -= 10;
        $appliedRules[] = ['rule' => '类型升贴水', 'condition' => '机采棉', 'discount' => -10];
    }
    
    return [
        'total_discount' => $totalDiscount,
        'applied_rules' => $appliedRules
    ];
}

// 输出计算结果
echo "=== 升贴水计算示例 ===\n\n";

foreach ($cottonSamples as $cotton) {
    echo "棉花样品：{$cotton['name']}\n";
    echo "基本指标：\n";
    echo "  长度：{$cotton['changdu']}mm\n";
    echo "  强力：{$cotton['qiangli']} cN/tex\n";
    echo "  马值：{$cotton['mazhi']}\n";
    echo "  回潮率：{$cotton['huichaolv']}%\n";
    echo "  含杂率：{$cotton['hanzalv']}%\n";
    echo "  整齐度：{$cotton['zhengqidu']}%\n";
    echo "  类型：{$cotton['leixing']}\n";
    echo "  原始基差：{$cotton['jicha']}\n\n";
    
    $result = calculatePremiumDiscount($cotton, $premiumDiscountRules);
    
    echo "升贴水计算结果：\n";
    foreach ($result['applied_rules'] as $rule) {
        $sign = $rule['discount'] > 0 ? '+' : '';
        echo "  {$rule['rule']} ({$rule['condition']}): {$sign}{$rule['discount']}\n";
    }
    
    $finalJicha = $cotton['jicha'] + $result['total_discount'];
    $sign = $result['total_discount'] > 0 ? '+' : '';
    
    echo "\n总升贴水：{$sign}{$result['total_discount']}\n";
    echo "调整后基差：{$cotton['jicha']} + ({$sign}{$result['total_discount']}) = {$finalJicha}\n";
    echo str_repeat("-", 50) . "\n\n";
}

echo "说明：\n";
echo "1. 升水（正值）表示价格上调\n";
echo "2. 贴水（负值）表示价格下调\n";
echo "3. 最终基差 = 原始基差 + 升贴水\n";
echo "4. 多个规则可以同时生效，升贴水值累加计算\n";
?>
